import { User } from './user.entity';
export declare enum ActivityNoteType {
    EVALUATION_COMMENT = "evaluation_comment",
    STATUS_UPDATE = "status_update",
    GENERAL_NOTE = "general_note",
    SYSTEM_LOG = "system_log",
    REVIEW_NOTE = "review_note",
    APPROVAL_NOTE = "approval_note",
    REJECTION_NOTE = "rejection_note"
}
export declare enum ActivityNoteStatus {
    ACTIVE = "active",
    ARCHIVED = "archived",
    DELETED = "deleted"
}
export declare class ActivityNote {
    id: string;
    entity_type: string;
    entity_id: string;
    note: string;
    note_type: string;
    status: string;
    category: string;
    step: string;
    metadata: Record<string, any>;
    priority: string;
    is_visible: boolean;
    is_internal: boolean;
    created_by_id: string;
    created_by: User;
    updated_by_id: string;
    updated_by: User;
    created_at: Date;
    updated_at: Date;
    archived_at: Date;
    deleted_at: Date;
}
