import { Address } from "./address.entity";
import { Contacts } from "./contacts.entity";
import { User } from "./user.entity";
export declare class Organization {
    organization_id: string;
    name: string;
    registration_number: string;
    website: string;
    email: string;
    phone: string;
    fax?: string;
    physical_address_id?: string;
    postal_address_id?: string;
    contact_id?: string;
    date_incorporation: Date;
    place_incorporation: string;
    created_by?: string;
    updated_by?: string;
    created_at?: Date;
    updated_at?: Date;
    deleted_at?: Date;
    physical_address?: Address;
    postal_address?: Address;
    contact?: Contacts;
    creator?: User;
    updater?: User;
    generateId(): void;
}
