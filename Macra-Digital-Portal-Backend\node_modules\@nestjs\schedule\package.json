{"name": "@nestjs/schedule", "version": "6.0.0", "description": "Nest - modern, fast, powerful node.js web framework (@schedule)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/schedule#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier **/**/*.ts --ignore-path ./.prettierignore --write", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:integration": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it"}, "dependencies": {"cron": "4.3.0"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.24.0", "@nestjs/common": "11.0.20", "@nestjs/core": "11.0.20", "@nestjs/platform-express": "11.0.20", "@nestjs/testing": "11.0.20", "@types/jest": "29.5.14", "@types/node": "22.14.1", "@types/sinon": "17.0.4", "eslint": "9.24.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.1", "prettier": "3.5.3", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "sinon": "20.0.0", "ts-jest": "29.3.2", "typescript": "5.8.3", "typescript-eslint": "8.30.1"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "https://github.com/nestjs/schedule"}}