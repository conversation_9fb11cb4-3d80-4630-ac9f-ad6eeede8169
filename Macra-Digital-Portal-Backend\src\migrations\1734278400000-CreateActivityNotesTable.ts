import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateActivityNotesTable1734278400000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'activity_notes',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: '(UUID())',
          },
          {
            name: 'entity_type',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'entity_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'note',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'note_type',
            type: 'enum',
            enum: [
              'evaluation_comment',
              'status_update',
              'general_note',
              'system_log',
              'review_note',
              'approval_note',
              'rejection_note',
            ],
            default: "'general_note'",
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['active', 'archived', 'deleted'],
            default: "'active'",
          },
          {
            name: 'category',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'step',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'priority',
            type: 'varchar',
            length: '20',
            default: "'normal'",
          },
          {
            name: 'is_visible',
            type: 'boolean',
            default: true,
          },
          {
            name: 'is_internal',
            type: 'boolean',
            default: false,
          },
          {
            name: 'created_by_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'updated_by_id',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'archived_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['created_by_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE',
          },
          {
            columnNames: ['updated_by_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE',
          },
        ],
      }),
      true,
    );

    // Create indexes for better performance
    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_entity',
        columnNames: ['entity_type', 'entity_id'],
      }),
    );

    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_created_by',
        columnNames: ['created_by_id'],
      }),
    );

    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_note_type',
        columnNames: ['note_type'],
      }),
    );

    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_step',
        columnNames: ['step'],
      }),
    );

    await queryRunner.createIndex(
      'activity_notes',
      new TableIndex({
        name: 'IDX_activity_notes_created_at',
        columnNames: ['created_at'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('activity_notes');
  }
}
