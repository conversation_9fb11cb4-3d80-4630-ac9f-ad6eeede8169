import { Injectable } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { EmailTemplateService } from './email-template.service';
import { NotificationType, RecipientType, NotificationStatus } from '../entities/notifications.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { assetsDir } from '../app.module';

@Injectable()
export class NotificationHelperService {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly emailTemplateService: EmailTemplateService,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Send application status notification to customer
   */
  async notifyApplicationStatus(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicantPhone: string,
    applicationNumber: string,
    status: string,
    createdBy: string,
    applicantName?: string,
    licenseType?: string,
    oldStatus?: string
  ): Promise<void> {

    // Determine which template to use based on status
    let emailTemplate: { subject: string; html: string };

    if (status === 'submitted') {
      emailTemplate = this.emailTemplateService.generateApplicationSubmittedTemplate({
        applicantName: applicantName || 'Valued Customer',
        applicationNumber,
        licenseType: licenseType || 'License',
        submissionDate: new Date().toLocaleDateString(),
        portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
      });
    } else {
      emailTemplate = this.emailTemplateService.generateApplicationStatusChangeTemplate({
        applicantName: applicantName || 'Valued Customer',
        applicationNumber,
        licenseType: licenseType || 'License',
        oldStatus: oldStatus || 'previous',
        newStatus: status,
        changeDate: new Date().toLocaleDateString(),
        portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
      });
    }

    const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;

    // Create email notification with template and send immediately
    if (applicantEmail) {
      console.log(`📧 NotificationHelper: Sending application status email to ${applicantEmail}`);
      try {
        console.log(`🔧 NotificationHelper: Attempting to send email to ${applicantEmail}`);
        console.log(`📧 Email subject: ${emailTemplate.subject}`);
        console.log(`📁 Assets directory: ${assetsDir}`);

        // Send email immediately
        await this.mailerService.sendMail({
          to: applicantEmail,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          attachments: [
            {
              filename: 'macra-logo.png',
              path: join(assetsDir, 'macra-logo.png'),
              cid: 'logo@macra',
            },
          ],
        } as any);

        console.log(`✅ NotificationHelper: Application status email sent successfully to ${applicantEmail}`);

        // Also create notification record for tracking
        const emailNotification = await this.notificationsService.create({
          type: NotificationType.EMAIL,
          recipient_type: RecipientType.CUSTOMER,
          recipient_id: applicantId,
          recipient_email: applicantEmail,
          subject: emailTemplate.subject,
          message,
          html_content: emailTemplate.html,
          entity_type: 'application',
          entity_id: applicationId,
          status: NotificationStatus.SENT,
        }, createdBy);

        // Update with sent timestamp
        await this.notificationsService.update(emailNotification.notification_id, {
          sent_at: new Date(),
        }, createdBy);
        console.log(`✅ NotificationHelper: Email notification record created with ID: ${emailNotification.notification_id}`);
      } catch (emailError) {
        console.error(`❌ NotificationHelper: Failed to send application status email to ${applicantEmail}:`);
        console.error(`❌ Error details:`, emailError);
        console.error(`❌ Error message:`, emailError.message);
        console.error(`❌ Error stack:`, emailError.stack);

        // Create notification record with failed status
        const failedNotification = await this.notificationsService.create({
          type: NotificationType.EMAIL,
          recipient_type: RecipientType.CUSTOMER,
          recipient_id: applicantId,
          recipient_email: applicantEmail,
          subject: emailTemplate.subject,
          message,
          html_content: emailTemplate.html,
          entity_type: 'application',
          entity_id: applicationId,
          status: NotificationStatus.FAILED,
        }, createdBy);

        // Update with error message
        await this.notificationsService.update(failedNotification.notification_id, {
          error_message: emailError.message,
        }, createdBy);

        throw emailError;
      }
    } else {
      console.log(`⚠️ NotificationHelper: No email address provided for applicant ${applicantId}`);
    }

    // Create in-app notification
    console.log(`📱 NotificationHelper: Creating in-app notification for applicant ${applicantId}`);
    try {
      const inAppNotification = await this.notificationsService.create({
        type: NotificationType.IN_APP,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        subject: emailTemplate.subject,
        message,
        entity_type: 'application',
        entity_id: applicationId,
        action_url: `/customer/my-licenses?application_id=${applicationId}`,
      }, createdBy);
      console.log(`✅ NotificationHelper: In-app notification created with ID: ${inAppNotification.notification_id}`);
    } catch (inAppError) {
      console.error(`❌ NotificationHelper: Failed to create in-app notification:`, inAppError);
      throw inAppError;
    }

    console.log(`🎉 NotificationHelper: All notifications processed for application ${applicationNumber}`);
  }

  /**
   * Send task assignment notification to staff
   */
  async notifyTaskAssignment(
    taskId: string,
    assigneeId: string,
    assigneeEmail: string,
    assigneePhone: string,
    taskTitle: string,
    taskDescription: string,
    createdBy: string,
    assigneeName?: string,
    applicationNumber?: string,
    applicantName?: string,
    priority?: string,
    dueDate?: string
  ): Promise<void> {
    const emailTemplate = this.emailTemplateService.generateTaskAssignedTemplate({
      assigneeName: assigneeName || 'Team Member',
      taskTitle,
      taskDescription,
      applicationNumber: applicationNumber || 'N/A',
      applicantName: applicantName || 'N/A',
      priority: priority || 'medium',
      dueDate,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
    });

    const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;

    // Create email notification with template and send immediately
    if (assigneeEmail) {
      console.log(`📧 NotificationHelper: Sending task assignment email to ${assigneeEmail}`);

      try {
        console.log(`🔧 NotificationHelper: Attempting to send task assignment email to ${assigneeEmail}`);
        console.log(`📧 Email subject: ${emailTemplate.subject}`);
        console.log(`📋 Task: ${taskTitle}`);

        // Send email immediately
        await this.mailerService.sendMail({
          to: assigneeEmail,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          attachments: [
            {
              filename: 'macra-logo.png',
              path: join(assetsDir, 'macra-logo.png'),
              cid: 'logo@macra',
            },
          ],
        } as any);

        console.log(`✅ NotificationHelper: Task assignment email sent successfully to ${assigneeEmail}`);

        // Also create notification record for tracking
        const emailNotification = await this.notificationsService.create({
          type: NotificationType.EMAIL,
          recipient_type: RecipientType.STAFF,
          recipient_id: assigneeId,
          recipient_email: assigneeEmail,
          subject: emailTemplate.subject,
          message,
          html_content: emailTemplate.html,
          entity_type: 'task',
          entity_id: taskId,
          status: NotificationStatus.SENT, // Mark as sent since we just sent it
        }, createdBy);

        // Update with sent timestamp
        await this.notificationsService.update(emailNotification.notification_id, {
          sent_at: new Date(),
        }, createdBy);

      } catch (error) {
        console.error(`❌ NotificationHelper: Failed to send task assignment email to ${assigneeEmail}:`);
        console.error(`❌ Error details:`, error);
        console.error(`❌ Error message:`, error.message);
        console.error(`❌ Error stack:`, error.stack);

        // Create notification record with failed status
        const failedNotification = await this.notificationsService.create({
          type: NotificationType.EMAIL,
          recipient_type: RecipientType.STAFF,
          recipient_id: assigneeId,
          recipient_email: assigneeEmail,
          subject: emailTemplate.subject,
          message,
          html_content: emailTemplate.html,
          entity_type: 'task',
          entity_id: taskId,
          status: NotificationStatus.FAILED,
        }, createdBy);

        // Update with error message
        await this.notificationsService.update(failedNotification.notification_id, {
          error_message: error.message,
        }, createdBy);

        throw error;
      }
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.STAFF,
      recipient_id: assigneeId,
      subject: emailTemplate.subject,
      message,
      entity_type: 'task',
      entity_id: taskId,
      action_url: `/tasks?task_id=${taskId}`,
    }, createdBy);
  }

  /**
   * Send license expiry notification to customer
   */
  async notifyLicenseExpiry(
    licenseId: string,
    customerId: string,
    customerEmail: string,
    customerPhone: string,
    licenseNumber: string,
    expiryDate: Date,
    daysUntilExpiry: number,
    createdBy: string
  ): Promise<void> {
    const subject = `License ${licenseNumber} Expiry Notice`;
    const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
    
    // Create email notification
    if (customerEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: customerId,
        recipient_email: customerEmail,
        subject,
        message,
        html_content: `<p>Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.</p>`,
        entity_type: 'license',
        entity_id: licenseId,
      }, createdBy);
    }

    // Create SMS notification
    if (customerPhone) {
      await this.notificationsService.create({
        type: NotificationType.SMS,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: customerId,
        recipient_phone: customerPhone,
        subject,
        message: `MACRA: ${message}`,
        entity_type: 'license',
        entity_id: licenseId,
      }, createdBy);
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER,
      recipient_id: customerId,
      subject,
      message,
      entity_type: 'license',
      entity_id: licenseId,
      action_url: `/customer/my-licenses?license_id=${licenseId}`,
    }, createdBy);
  }

  /**
   * Send task completion notification to applicant and assignee
   */
  async notifyTaskCompletion(
    taskId: string,
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    assigneeId: string,
    assigneeEmail: string,
    taskTitle: string,
    applicationNumber: string,
    outcome: string,
    createdBy: string,
    applicantName?: string,
    licenseType?: string,
    comments?: string,
    nextSteps?: string
  ): Promise<void> {
    const emailTemplate = this.emailTemplateService.generateTaskCompletedTemplate({
      applicantName: applicantName || 'Valued Customer',
      taskTitle,
      applicationNumber,
      licenseType: licenseType || 'License',
      completionDate: new Date().toLocaleDateString(),
      outcome,
      comments,
      nextSteps,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
    });

    const message = `Task "${taskTitle}" for application ${applicationNumber} has been completed with outcome: ${outcome}`;

    // Notify applicant
    if (applicantEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        recipient_email: applicantEmail,
        subject: emailTemplate.subject,
        message,
        html_content: emailTemplate.html,
        entity_type: 'application',
        entity_id: applicationId,
      }, createdBy);

      // In-app notification for applicant
      await this.notificationsService.create({
        type: NotificationType.IN_APP,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        subject: emailTemplate.subject,
        message,
        entity_type: 'application',
        entity_id: applicationId,
        action_url: `/customer/my-licenses?application_id=${applicationId}`,
      }, createdBy);
    }

    // Notify assignee (confirmation)
    if (assigneeEmail && assigneeId !== createdBy) {
      const assigneeMessage = `You have successfully completed task "${taskTitle}" for application ${applicationNumber}`;

      await this.notificationsService.create({
        type: NotificationType.IN_APP,
        recipient_type: RecipientType.STAFF,
        recipient_id: assigneeId,
        subject: `Task Completed: ${taskTitle}`,
        message: assigneeMessage,
        entity_type: 'task',
        entity_id: taskId,
        action_url: `/tasks?task_id=${taskId}`,
      }, createdBy);
    }
  }

  /**
   * Send license approval notification to customer
   */
  async notifyLicenseApproval(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicationNumber: string,
    licenseNumber: string,
    licenseType: string,
    createdBy: string,
    applicantName?: string,
    expiryDate?: string
  ): Promise<void> {
    const emailTemplate = this.emailTemplateService.generateLicenseApprovedTemplate({
      applicantName: applicantName || 'Valued Customer',
      applicationNumber,
      licenseType,
      licenseNumber,
      approvalDate: new Date().toLocaleDateString(),
      expiryDate: expiryDate || 'TBD',
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`,
    });

    const message = `Congratulations! Your license application ${applicationNumber} has been approved. License Number: ${licenseNumber}`;

    // Create email notification with template
    if (applicantEmail) {
      await this.notificationsService.create({
        type: NotificationType.EMAIL,
        recipient_type: RecipientType.CUSTOMER,
        recipient_id: applicantId,
        recipient_email: applicantEmail,
        subject: emailTemplate.subject,
        message,
        html_content: emailTemplate.html,
        entity_type: 'application',
        entity_id: applicationId,
      }, createdBy);
    }

    // Create in-app notification
    await this.notificationsService.create({
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER,
      recipient_id: applicantId,
      subject: emailTemplate.subject,
      message,
      entity_type: 'application',
      entity_id: applicationId,
      action_url: `/customer/my-licenses?license_number=${licenseNumber}`,
    }, createdBy);
  }
}
