import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MailerService } from '@nestjs-modules/mailer';
import { Notifications, NotificationStatus, NotificationType } from '../entities/notifications.entity';
import { join } from 'path';
import { assetsDir } from '../app.module';

@Injectable()
export class NotificationProcessorService {
  private readonly logger = new Logger(NotificationProcessorService.name);

  constructor(
    @InjectRepository(Notifications)
    private notificationsRepository: Repository<Notifications>,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Process pending email notifications every minute
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async processPendingNotifications(): Promise<void> {
    this.logger.log('🔄 Processing pending email notifications...');

    try {
      // Get all pending email notifications
      const pendingNotifications = await this.notificationsRepository.find({
        where: {
          type: NotificationType.EMAIL,
          status: NotificationStatus.PENDING,
        },
        order: {
          created_at: 'ASC',
        },
        take: 50, // Process max 50 at a time to avoid overwhelming the email service
      });

      if (pendingNotifications.length === 0) {
        this.logger.debug('📭 No pending email notifications found');
        return;
      }

      this.logger.log(`📧 Found ${pendingNotifications.length} pending email notifications`);

      for (const notification of pendingNotifications) {
        await this.processEmailNotification(notification);
      }

      this.logger.log(`✅ Finished processing ${pendingNotifications.length} notifications`);
    } catch (error) {
      this.logger.error('❌ Error processing pending notifications:', error);
    }
  }

  /**
   * Process a single email notification
   */
  private async processEmailNotification(notification: Notifications): Promise<void> {
    try {
      this.logger.log(`📤 Sending email to ${notification.recipient_email} - ${notification.subject}`);

      // Update status to sending
      await this.notificationsRepository.update(notification.notification_id, {
        status: NotificationStatus.SENT, // We'll update this based on success/failure
        retry_count: (notification.retry_count || 0) + 1,
      });

      // Send the email
      await this.mailerService.sendMail({
        to: notification.recipient_email,
        subject: notification.subject,
        html: notification.html_content || notification.message,
        attachments: [
          {
            filename: 'macra-logo.png',
            path: join(assetsDir, 'macra-logo.png'),
            cid: 'logo@macra',
          },
        ],
      } as any);

      // Update status to sent
      await this.notificationsRepository.update(notification.notification_id, {
        status: NotificationStatus.SENT,
        sent_at: new Date(),
        error_message: undefined,
      });

      this.logger.log(`✅ Email sent successfully to ${notification.recipient_email}`);
    } catch (error) {
      this.logger.error(`❌ Failed to send email to ${notification.recipient_email}:`, error);

      // Update status to failed
      await this.notificationsRepository.update(notification.notification_id, {
        status: NotificationStatus.FAILED,
        error_message: error.message,
        retry_count: (notification.retry_count || 0) + 1,
      });

      // If retry count is less than 3, set back to pending for retry
      if ((notification.retry_count || 0) < 3) {
        setTimeout(async () => {
          await this.notificationsRepository.update(notification.notification_id, {
            status: NotificationStatus.PENDING,
          });
          this.logger.log(`🔄 Notification ${notification.notification_id} queued for retry`);
        }, 60000); // Retry after 1 minute
      }
    }
  }

  /**
   * Manually process all pending notifications (for testing)
   */
  async processAllPendingNotifications(): Promise<{ processed: number; failed: number }> {
    this.logger.log('🚀 Manually processing all pending notifications...');

    const pendingNotifications = await this.notificationsRepository.find({
      where: {
        type: NotificationType.EMAIL,
        status: NotificationStatus.PENDING,
      },
      order: {
        created_at: 'ASC',
      },
    });

    let processed = 0;
    let failed = 0;

    for (const notification of pendingNotifications) {
      try {
        await this.processEmailNotification(notification);
        processed++;
      } catch (error) {
        failed++;
        this.logger.error(`Failed to process notification ${notification.notification_id}:`, error);
      }
    }

    this.logger.log(`✅ Manual processing complete: ${processed} processed, ${failed} failed`);
    return { processed, failed };
  }

  /**
   * Get notification processing statistics
   */
  async getProcessingStats(): Promise<{
    pending: number;
    sent: number;
    failed: number;
    total: number;
  }> {
    const [pending, sent, failed, total] = await Promise.all([
      this.notificationsRepository.count({
        where: { type: NotificationType.EMAIL, status: NotificationStatus.PENDING },
      }),
      this.notificationsRepository.count({
        where: { type: NotificationType.EMAIL, status: NotificationStatus.SENT },
      }),
      this.notificationsRepository.count({
        where: { type: NotificationType.EMAIL, status: NotificationStatus.FAILED },
      }),
      this.notificationsRepository.count({
        where: { type: NotificationType.EMAIL },
      }),
    ]);

    return { pending, sent, failed, total };
  }
}
